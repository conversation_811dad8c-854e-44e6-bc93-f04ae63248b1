<template>
  <AppLayout>
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <i class="pi pi-spinner pi-spin text-4xl text-primary"></i>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="mb-6">
      <Message severity="error" :closable="false">
        {{ error }}
      </Message>
    </div>

    <!-- No Project Selected -->
    <div v-else-if="!currentProject" class="text-center py-12">
      <div class="mb-6">
        <i class="pi pi-folder-open text-6xl text-surface-400 mb-4"></i>
        <h2 class="text-2xl font-semibold text-surface-700 dark:text-surface-300 mb-2">
          {{ t('project.noProjectSelected') }}
        </h2>
        <p class="text-surface-600 dark:text-surface-400 mb-6 max-w-md mx-auto">
          {{ t('project.selectProjectDescription') }}
        </p>
        <Button
          :label="t('dashboard.yourProjects')"
          icon="pi pi-arrow-left"
          @click="router.push('/')"
        />
      </div>
    </div>

    <!-- Events Content -->
    <div v-else>
      <!-- Project Header -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <Button
              icon="pi pi-arrow-left"
              text
              @click="router.push(`/projects/${currentProject.id}/dashboard`)"
              class="text-surface-600 dark:text-surface-400"
            />
            <div>
              <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
                {{ t('events.title') }}
              </h1>
              <p class="text-surface-600 dark:text-surface-400 mt-1">
                {{ currentProject.name }} • {{ projectEvents.length }}
                {{ t('events.title').toLowerCase() }}
              </p>
            </div>
          </div>
          <Button :label="t('events.createEvent')" icon="pi pi-plus" @click="createEvent" />
        </div>
      </div>

      <!-- Events List -->
      <Card v-if="projectEvents.length > 0">
        <template #content>
          <div class="space-y-4">
            <div
              v-for="event in projectEvents"
              :key="event.id"
              class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors cursor-pointer"
              @click="viewEvent(event.id)"
            >
              <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <div
                  class="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center"
                >
                  <i class="pi pi-calendar text-primary-600 dark:text-primary-400"></i>
                </div>
                <div>
                  <h4 class="font-semibold text-surface-900 dark:text-surface-0">
                    {{ event.name }}
                  </h4>
                  <p class="text-sm text-surface-600 dark:text-surface-400">
                    {{ formatEventDate(event.date) }} • {{ event.guestCount || 0 }}
                    {{ t('guests.title').toLowerCase() }}
                  </p>
                  <p v-if="event.location" class="text-xs text-surface-500 mt-1">
                    <i class="pi pi-map-marker mr-1"></i>{{ event.location }}
                  </p>
                </div>
              </div>
              <div class="flex space-x-2 rtl:space-x-reverse">
                <Button icon="pi pi-eye" text size="small" @click.stop="viewEvent(event.id)" />
                <Button icon="pi pi-pencil" text size="small" @click.stop="editEvent(event.id)" />
                <Button
                  icon="pi pi-trash"
                  text
                  size="small"
                  severity="danger"
                  @click.stop="deleteEvent(event.id)"
                />
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Empty State -->
      <Card v-else>
        <template #content>
          <div class="text-center py-12">
            <i class="pi pi-calendar text-6xl text-surface-400 mb-4"></i>
            <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
              {{ t('events.noEventsInProject') }}
            </h3>
            <p class="text-surface-600 dark:text-surface-400 mb-6">
              Create your first event for {{ currentProject.name }} to get started
            </p>
            <Button :label="t('events.createFirstEvent')" icon="pi pi-plus" @click="createEvent" />
          </div>
        </template>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useEventsStore } from '@/stores/events'
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Message from 'primevue/message'

const router = useRouter()
const route = useRoute()
const projectsStore = useProjectsStore()
const eventsStore = useEventsStore()
const { t } = useLocale()

// Computed
const currentProject = computed(() => projectsStore.currentProject)
const isLoading = computed(() => projectsStore.isLoading || eventsStore.isLoading)
const error = computed(() => projectsStore.error || eventsStore.error)

const projectEvents = computed(() => {
  if (!currentProject.value) return []
  return eventsStore.events.filter((event) => event.projectId === currentProject.value?.id)
})

// Methods
const formatEventDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

const createEvent = () => {
  if (currentProject.value) {
    router.push(`/projects/${currentProject.value.id}/events/create`)
  }
}

const viewEvent = (eventId: string) => {
  if (currentProject.value) {
    router.push(`/projects/${currentProject.value.id}/events/${eventId}`)
  }
}

const editEvent = (eventId: string) => {
  if (currentProject.value) {
    router.push(`/projects/${currentProject.value.id}/events/${eventId}/edit`)
  }
}

const deleteEvent = async (eventId: string) => {
  if (confirm(t('events.confirmDeleteEvent'))) {
    try {
      await eventsStore.deleteEvent(eventId)
    } catch (error) {
      console.error('Error deleting event:', error)
    }
  }
}

// Lifecycle
onMounted(async () => {
  const projectId = route.params.projectId as string

  if (projectId) {
    // Ensure projects are loaded
    if (!projectsStore.hasProjects) {
      await projectsStore.loadUserProjects()
    }

    // Set current project
    projectsStore.setCurrentProject(projectId)

    // Load project events
    if (currentProject.value) {
      await eventsStore.loadProjectEvents(projectId)
    }
  }
})
</script>
