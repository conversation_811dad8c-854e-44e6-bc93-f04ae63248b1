import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import Lara from '@primeuix/themes/lara'
import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'
import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import ar from './locales/ar.json'
import he from './locales/he.json'

// Import dark mode composable to initialize it early
import './composables/useDarkMode'

// PrimeVue Components
import Button from 'primevue/button'
import Card from 'primevue/card'
import Select from 'primevue/select'
import Menu from 'primevue/menu'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import DatePicker from 'primevue/datepicker'
import Dialog from 'primevue/dialog'
import Toast from 'primevue/toast'
import ToastService from 'primevue/toastservice'

// Create i18n instance
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en,
    ar,
    he,
  },
})

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(PrimeVue, {
  theme: {
    preset: Lara,
    options: {
      darkModeSelector: '.dark',
    },
  },
})
app.use(ToastService)

// Register PrimeVue components globally
app.component('Button', Button)
app.component('Card', Card)
app.component('Select', Select)
app.component('Menu', Menu)
app.component('DataTable', DataTable)
app.component('Column', Column)
app.component('InputText', InputText)
app.component('DatePicker', DatePicker)
app.component('Dialog', Dialog)
app.component('Toast', Toast)

// Initialize the auth store and wait for it to be ready
const authStore = useAuthStore(pinia)

// Initialize locale from localStorage
const savedLocale = localStorage.getItem('locale')
if (savedLocale && ['en', 'ar', 'he'].includes(savedLocale)) {
  i18n.global.locale.value = savedLocale as any
}

// Set initial document direction and language
const rtlLanguages = ['ar', 'he']
const currentLocale = i18n.global.locale.value
document.documentElement.dir = rtlLanguages.includes(currentLocale) ? 'rtl' : 'ltr'
document.documentElement.lang = currentLocale
if (rtlLanguages.includes(currentLocale)) {
  document.body.classList.add('rtl')
} else {
  document.body.classList.add('ltr')
}

// Wait for auth to be ready, then mount the app
authStore
  .initAuth()
  .then(() => {
    app.mount('#app')
  })
  .catch((error) => {
    console.error('Error initializing auth:', error)
    // Mount app anyway to prevent hanging
    app.mount('#app')
  })
