<template>
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
          Edit Event
        </h1>
        <p class="text-lg text-surface-600 dark:text-surface-400 max-w-2xl mx-auto">
          Update your event details and settings.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !event" class="flex justify-center items-center py-12">
        <ProgressSpinner />
      </div>

      <!-- Error State -->
      <Message v-else-if="errorMessage && !event" severity="error" :closable="false" class="mb-6">
        {{ errorMessage }}
      </Message>

      <!-- Main Content -->
      <div v-else-if="event" class="space-y-8">
        <!-- Event Details Form -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Event Details
              </h2>
              <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                Update your event information
              </p>
            </div>
          </template>

          <template #content>
            <form @submit.prevent="updateEvent" class="space-y-6">
              <!-- Event Name -->
              <div>
                <label
                  for="eventName"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Name *
                </label>
                <InputText
                  id="eventName"
                  v-model="form.eventName"
                  placeholder="e.g., Wedding Ceremony, Reception, Rehearsal Dinner"
                  class="w-full"
                  :invalid="!!errors.eventName"
                  required
                />
                <small v-if="errors.eventName" class="text-red-500 mt-1 block">{{
                  errors.eventName
                }}</small>
              </div>

              <!-- Event Type -->
              <div>
                <label
                  for="eventType"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Type *
                </label>
                <Dropdown
                  id="eventType"
                  v-model="form.eventType"
                  :options="eventTypes"
                  option-label="label"
                  option-value="value"
                  placeholder="Select event type"
                  class="w-full"
                  :invalid="!!errors.eventType"
                  required
                />
                <small v-if="errors.eventType" class="text-red-500 mt-1 block">{{
                  errors.eventType
                }}</small>
              </div>

              <!-- Event Date -->
              <div>
                <label
                  for="eventDate"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Date *
                </label>
                <DatePicker
                  id="eventDate"
                  v-model="form.eventDate"
                  :min-date="new Date()"
                  date-format="mm/dd/yy"
                  placeholder="Select event date"
                  class="w-full"
                  :invalid="!!errors.eventDate"
                  required
                />
                <small v-if="errors.eventDate" class="text-red-500 mt-1 block">{{
                  errors.eventDate
                }}</small>
              </div>

              <!-- Event Time -->
              <div>
                <label
                  for="eventTime"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Time (Optional)
                </label>
                <InputText
                  id="eventTime"
                  v-model="form.eventTime"
                  placeholder="e.g., 3:00 PM, 6:30 PM"
                  class="w-full"
                />
              </div>

              <!-- Venue -->
              <div>
                <label
                  for="venue"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Venue (Optional)
                </label>
                <InputText
                  id="venue"
                  v-model="form.venue"
                  placeholder="e.g., St. Mary's Church, Grand Ballroom"
                  class="w-full"
                />
              </div>

              <!-- Description -->
              <div>
                <label
                  for="description"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Description (Optional)
                </label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  placeholder="Add any additional details about this event..."
                  class="w-full"
                  rows="3"
                />
              </div>

              <!-- Event Status -->
              <div>
                <label
                  for="eventStatus"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Status
                </label>
                <Dropdown
                  id="eventStatus"
                  v-model="form.eventStatus"
                  :options="statusOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Select event status"
                  class="w-full"
                />
              </div>

              <!-- Error Message -->
              <Message v-if="updateErrorMessage" severity="error" :closable="false">
                {{ updateErrorMessage }}
              </Message>

              <!-- Success Message -->
              <Message v-if="successMessage" severity="success" :closable="false">
                {{ successMessage }}
              </Message>

              <!-- Submit Button -->
              <div class="flex justify-between items-center pt-4">
                <Button
                  type="button"
                  severity="secondary"
                  @click="$router.push(`/projects/${event.projectId}/events/${event.id}`)"
                >
                  <i class="pi pi-arrow-left mr-2"></i>
                  Back to Event
                </Button>

                <div class="flex space-x-3">
                  <Button
                    type="button"
                    severity="danger"
                    outlined
                    @click="confirmDelete"
                    :disabled="isUpdating"
                  >
                    <i class="pi pi-trash mr-2"></i>
                    Delete Event
                  </Button>

                  <Button type="submit" :loading="isUpdating" :disabled="isUpdating">
                    <i class="pi pi-save mr-2"></i>
                    Save Changes
                  </Button>
                </div>
              </div>
            </form>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useConfirm } from 'primevue/useconfirm'
import type { EventBasic } from '@/types/user'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import DatePicker from 'primevue/datepicker'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const confirm = useConfirm()

// Reactive data
const event = ref<EventBasic | null>(null)
const isLoading = ref(true)
const isUpdating = ref(false)
const errorMessage = ref('')
const updateErrorMessage = ref('')
const successMessage = ref('')

// Form data
const form = ref({
  eventName: '',
  eventType: '',
  eventDate: null as Date | null,
  eventTime: '',
  venue: '',
  description: '',
  eventStatus: '',
})

// Form validation errors
const errors = ref({
  eventName: '',
  eventType: '',
  eventDate: '',
})

// Event type options
const eventTypes = [
  { label: 'Wedding Ceremony', value: 'ceremony' },
  { label: 'Reception', value: 'reception' },
  { label: 'Rehearsal Dinner', value: 'rehearsal' },
  { label: 'Engagement Party', value: 'party' },
  { label: 'Other', value: 'other' },
]

// Status options
const statusOptions = [
  { label: 'Planning', value: 'planning' },
  { label: 'Active', value: 'active' },
  { label: 'Completed', value: 'completed' },
  { label: 'Cancelled', value: 'cancelled' },
]

// Methods
const loadEvent = async () => {
  const eventId = route.params.eventId as string
  const projectId = route.params.projectId as string
  
  if (!eventId || !projectId) {
    errorMessage.value = 'Event ID and Project ID are required'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    // TODO: Implement actual event loading service
    // For now, create a mock event
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockEvent: EventBasic = {
      id: eventId,
      projectId: projectId,
      name: 'Wedding Ceremony',
      type: 'ceremony',
      date: { seconds: Date.now() / 1000, nanoseconds: 0 },
      time: '3:00 PM',
      venue: 'St. Mary\'s Church',
      description: 'The main wedding ceremony where we exchange vows.',
      status: 'planning',
      guestCount: 150,
      confirmedGuests: 75,
      pendingGuests: 50,
      createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 },
      updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 },
      createdBy: authStore.user?.uid || ''
    }
    
    event.value = mockEvent
    
    // Populate form with event data
    form.value = {
      eventName: mockEvent.name,
      eventType: mockEvent.type,
      eventDate: new Date(mockEvent.date.seconds * 1000),
      eventTime: mockEvent.time || '',
      venue: mockEvent.venue || '',
      description: mockEvent.description || '',
      eventStatus: mockEvent.status,
    }
    
  } catch (error) {
    console.error('Error loading event:', error)
    errorMessage.value = 'Failed to load event'
  } finally {
    isLoading.value = false
  }
}

const validateForm = (): boolean => {
  errors.value = {
    eventName: '',
    eventType: '',
    eventDate: '',
  }

  let isValid = true

  if (!form.value.eventName.trim()) {
    errors.value.eventName = 'Event name is required'
    isValid = false
  }

  if (!form.value.eventType) {
    errors.value.eventType = 'Event type is required'
    isValid = false
  }

  if (!form.value.eventDate) {
    errors.value.eventDate = 'Event date is required'
    isValid = false
  }

  return isValid
}

const updateEvent = async () => {
  if (!validateForm() || !event.value) return

  isUpdating.value = true
  updateErrorMessage.value = ''
  successMessage.value = ''

  try {
    // TODO: Implement actual event update service
    console.log('Updating event:', {
      eventId: event.value.id,
      ...form.value,
    })

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    successMessage.value = 'Event updated successfully!'
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  } catch (error) {
    console.error('Error updating event:', error)
    updateErrorMessage.value = 'Failed to update event. Please try again.'
  } finally {
    isUpdating.value = false
  }
}

const confirmDelete = () => {
  confirm.require({
    message: 'Are you sure you want to delete this event? This action cannot be undone.',
    header: 'Delete Event',
    icon: 'pi pi-exclamation-triangle',
    rejectClass: 'p-button-secondary p-button-outlined',
    acceptClass: 'p-button-danger',
    accept: deleteEvent,
  })
}

const deleteEvent = async () => {
  if (!event.value) return

  try {
    // TODO: Implement actual event deletion service
    console.log('Deleting event:', event.value.id)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Redirect to events list
    router.push(`/projects/${event.value.projectId}/events`)
  } catch (error) {
    console.error('Error deleting event:', error)
    updateErrorMessage.value = 'Failed to delete event. Please try again.'
  }
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  loadEvent()
})
</script>
