<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Top Navigation Bar -->
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-3">
      <div class="flex items-center justify-between">
        <!-- Logo and Brand -->
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
          <Button icon="pi pi-bars" text @click="toggleSidebar" class="lg:hidden" />
          <div class="flex items-center space-x-2 rtl:space-x-reverse">
            <i class="pi pi-calendar text-2xl text-primary"></i>
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">
              {{ t('common.appName', 'EventManager') }}
            </h1>
          </div>
        </div>

        <!-- Project Selector -->
        <div v-if="projectsStore.hasProjects" class="flex-1 max-w-xs mx-4">
          <Select
            v-model="selectedProjectId"
            :options="projectsStore.projects"
            option-label="name"
            option-value="id"
            :placeholder="t('navigation.selectProject')"
            @change="onProjectChange"
            class="w-full"
          >
            <template #value="slotProps">
              <div v-if="slotProps.value" class="flex items-center space-x-2 rtl:space-x-reverse">
                <div
                  class="w-6 h-6 bg-primary-100 dark:bg-primary-900/20 rounded flex items-center justify-center"
                >
                  <i class="pi pi-folder text-primary-600 dark:text-primary-400 text-xs"></i>
                </div>
                <span class="text-sm font-medium truncate">{{ getCurrentProjectName() }}</span>
              </div>
              <div v-else class="flex items-center space-x-2 rtl:space-x-reverse text-gray-500">
                <i class="pi pi-folder text-sm"></i>
                <span class="text-sm">{{ t('navigation.selectProject') }}</span>
              </div>
            </template>
            <template #option="slotProps">
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                  <div
                    class="w-6 h-6 bg-primary-100 dark:bg-primary-900/20 rounded flex items-center justify-center"
                  >
                    <i class="pi pi-folder text-primary-600 dark:text-primary-400 text-xs"></i>
                  </div>
                  <div>
                    <div class="font-medium">{{ slotProps.option.name }}</div>
                    <div class="text-xs text-gray-500">
                      {{ formatProjectDate(slotProps.option.weddingDate) }}
                    </div>
                  </div>
                </div>
                <span
                  :class="getTierBadgeClass(slotProps.option.tier)"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                >
                  {{ slotProps.option.tier.toUpperCase() }}
                </span>
              </div>
            </template>
          </Select>
        </div>

        <!-- Top Navigation Actions -->
        <div class="flex items-center space-x-4 rtl:space-x-reverse">
          <!-- Language Selector -->
          <Select
            v-model="selectedLocale"
            :options="availableLocales"
            option-label="name"
            option-value="code"
            @change="onLocaleChange"
            class="w-32"
          >
            <template #value="slotProps">
              <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <span>{{ slotProps.value?.flag || getLocaleFlag(selectedLocale) }}</span>
                <span class="text-sm">{{ getLocaleName(selectedLocale) }}</span>
              </div>
            </template>
            <template #option="slotProps">
              <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <span>{{ slotProps.option.flag }}</span>
                <span>{{ slotProps.option.name }}</span>
              </div>
            </template>
          </Select>

          <!-- Dark Mode Toggle -->
          <Button
            :icon="isDarkMode ? 'pi pi-sun' : 'pi pi-moon'"
            text
            @click="toggleDarkMode"
            :title="isDarkMode ? t('common.lightMode') : t('common.darkMode')"
            class="p-button-rounded"
          />

          <!-- Notifications -->
          <Button
            icon="pi pi-bell"
            text
            badge="3"
            badge-class="p-badge-danger"
            @click="toggleNotifications"
          />

          <!-- User Menu -->
          <Menu ref="userMenu" :model="userMenuItems" :popup="true">
            <template #item="{ item }">
              <div class="flex items-center space-x-2 rtl:space-x-reverse p-2">
                <i :class="item.icon"></i>
                <span>{{ item.label }}</span>
              </div>
            </template>
          </Menu>
          <Button icon="pi pi-user" text @click="toggleUserMenu" class="p-button-rounded" />
        </div>
      </div>
    </div>

    <div class="flex">
      <!-- Sidebar -->
      <aside
        :class="[
          'bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300',
          sidebarVisible ? 'w-64' : 'w-0 lg:w-16',
          'lg:relative absolute z-50 h-[calc(100vh-4rem)]',
        ]"
      >
        <nav class="p-4 space-y-2">
          <div v-for="item in navigationItems" :key="item.route" class="relative">
            <router-link
              :to="item.route"
              :class="[
                'flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-lg transition-colors duration-200',
                'hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path === item.route
                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-r-2 border-primary-600'
                  : 'text-gray-700 dark:text-gray-200',
              ]"
              @click="closeSidebarOnMobile"
            >
              <i :class="[item.icon, 'text-lg']"></i>
              <span v-show="sidebarVisible" class="font-medium transition-opacity duration-200">
                {{ t(item.label) }}
              </span>
            </router-link>
          </div>
        </nav>

        <!-- Sidebar Footer -->
        <div class="absolute bottom-4 left-4 right-4" v-show="sidebarVisible">
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
            <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">
              {{ t('subscription.currentPlan') }}
            </div>
            <div class="font-semibold text-sm text-gray-900 dark:text-white">Pro Plan</div>
            <Button
              :label="t('subscription.upgradePlan')"
              size="small"
              class="w-full mt-2"
              outlined
            />
          </div>
        </div>
      </aside>

      <!-- Main Content -->
      <main class="flex-1 overflow-auto">
        <div class="p-6">
          <slot />
        </div>
      </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div
      v-if="sidebarVisible"
      class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
      @click="closeSidebar"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useProjectsStore } from '@/stores/projects'
import { useLocale } from '@/composables/useLocale'
import { useDarkMode } from '@/composables/useDarkMode'
import Button from 'primevue/button'
import Select from 'primevue/select'
import Menu from 'primevue/menu'
import type { Timestamp } from 'firebase/firestore'

const router = useRouter()
const authStore = useAuthStore()
const projectsStore = useProjectsStore()
const { locale, availableLocales, changeLocale, t } = useLocale()
const { isDarkMode, toggleDarkMode } = useDarkMode()

const sidebarVisible = ref(true)
const userMenu = ref()
const selectedLocale = ref(locale.value)
const selectedProjectId = ref(projectsStore.currentProject?.id || '')

const navigationItems = [
  { route: '/', icon: 'pi pi-home', label: 'navigation.dashboard' },
  { route: '/events', icon: 'pi pi-calendar', label: 'navigation.events' },
  { route: '/guests', icon: 'pi pi-users', label: 'navigation.guests' },
  { route: '/analytics', icon: 'pi pi-chart-bar', label: 'navigation.analytics' },
  { route: '/collaborators', icon: 'pi pi-user-plus', label: 'navigation.collaborators' },
  { route: '/billing', icon: 'pi pi-credit-card', label: 'navigation.billing' },
  { route: '/settings', icon: 'pi pi-cog', label: 'navigation.settings' },
]

const userMenuItems = computed(() => [
  {
    label: t('navigation.profile'),
    icon: 'pi pi-user',
    command: () => router.push('/profile'),
  },
  {
    label: t('navigation.settings'),
    icon: 'pi pi-cog',
    command: () => router.push('/settings'),
  },
  { separator: true },
  {
    label: t('navigation.logout'),
    icon: 'pi pi-sign-out',
    command: handleLogout,
  },
])

const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

const closeSidebar = () => {
  sidebarVisible.value = false
}

const closeSidebarOnMobile = () => {
  if (window.innerWidth < 1024) {
    closeSidebar()
  }
}

const toggleUserMenu = (event: Event) => {
  userMenu.value.toggle(event)
}

const toggleNotifications = () => {
  // TODO: Implement notifications panel
  console.log('Toggle notifications')
}

const onLocaleChange = () => {
  changeLocale(selectedLocale.value)
}

const getLocaleFlag = (code: string) => {
  const locale = availableLocales.find((l) => l.code === code)
  return locale?.flag || '🌐'
}

const getLocaleName = (code: string) => {
  const locale = availableLocales.find((l) => l.code === code)
  return locale?.name || code
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

// Project selector methods
const getCurrentProjectName = () => {
  return projectsStore.currentProject?.name || ''
}

const formatProjectDate = (timestamp: Timestamp) => {
  const date = new Date(timestamp.seconds * 1000)
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  }).format(date)
}

const getTierBadgeClass = (tier: string) => {
  const classes = {
    free: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    basic: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200',
    premium: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200',
    gold: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200',
  }
  return classes[tier as keyof typeof classes] || classes.free
}

const onProjectChange = () => {
  if (selectedProjectId.value) {
    projectsStore.setCurrentProject(selectedProjectId.value)
    router.push(`/projects/${selectedProjectId.value}/dashboard`)
  }
}

onMounted(async () => {
  // Close sidebar on mobile by default
  if (window.innerWidth < 1024) {
    sidebarVisible.value = false
  }

  // Load user projects for project selector
  if (authStore.isAuthenticated && !projectsStore.hasProjects) {
    await projectsStore.loadUserProjects()
  }

  // Sync selected project with store
  if (projectsStore.currentProject) {
    selectedProjectId.value = projectsStore.currentProject.id
  }
})
</script>

<style scoped>
/* Additional component-specific styles if needed */
</style>
