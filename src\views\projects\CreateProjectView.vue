<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
          Create Your Wedding Project
        </h1>
        <p class="text-lg text-surface-600 dark:text-surface-400 max-w-2xl mx-auto">
          Choose the perfect plan for your special day. You can upgrade anytime as your needs grow.
        </p>
      </div>

      <!-- Tier Selection -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <div
          v-for="tier in tiers"
          :key="tier.id"
          @click="selectedTier = tier.id"
          :class="[
            'relative cursor-pointer rounded-2xl border-2 p-6 transition-all duration-200',
            selectedTier === tier.id
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-950 shadow-lg scale-105'
              : 'border-surface-200 dark:border-surface-700 bg-white dark:bg-surface-800 hover:border-primary-300 hover:shadow-md',
          ]"
        >
          <!-- Popular Badge -->
          <div v-if="tier.popular" class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span class="bg-primary-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              Most Popular
            </span>
          </div>

          <!-- Tier Header -->
          <div class="text-center mb-6">
            <h3 class="text-xl font-bold text-surface-900 dark:text-surface-0 mb-2">
              {{ tier.name }}
            </h3>
            <div class="mb-3">
              <span class="text-3xl font-bold text-surface-900 dark:text-surface-0">
                {{ ProjectTierManager.formatPrice(tier) }}
              </span>
              <span v-if="tier.price > 0" class="text-surface-500 dark:text-surface-400 ml-1">
                one-time
              </span>
            </div>
            <p class="text-sm text-surface-600 dark:text-surface-400">
              {{ tier.description }}
            </p>
          </div>

          <!-- Features List -->
          <div class="space-y-3">
            <div class="flex items-center text-sm">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-surface-700 dark:text-surface-300">
                {{ tier.features.maxEvents }} event{{ tier.features.maxEvents === 1 ? '' : 's' }}
              </span>
            </div>

            <div class="flex items-center text-sm">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-surface-700 dark:text-surface-300">
                {{ ProjectTierManager.formatGuestLimit(tier.features.maxGuests) }} guests
              </span>
            </div>

            <div class="flex items-center text-sm">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-surface-700 dark:text-surface-300">
                {{ tier.features.maxCollaborators }} collaborator{{
                  tier.features.maxCollaborators === 1 ? '' : 's'
                }}
              </span>
            </div>

            <div class="flex items-center text-sm">
              <i class="pi pi-check text-green-500 mr-2"></i>
              <span class="text-surface-700 dark:text-surface-300">
                {{ ProjectTierManager.formatRetention(tier.features.dataRetentionWeeks) }} data
                retention
              </span>
            </div>

            <div class="flex items-center text-sm">
              <i
                :class="
                  tier.features.hasProjections
                    ? 'pi pi-check text-green-500'
                    : 'pi pi-times text-surface-400'
                "
                class="mr-2"
              ></i>
              <span
                :class="
                  tier.features.hasProjections
                    ? 'text-surface-700 dark:text-surface-300'
                    : 'text-surface-400'
                "
              >
                Guest projections
              </span>
            </div>

            <div class="flex items-center text-sm">
              <i
                :class="
                  tier.features.hasAdvancedAnalytics
                    ? 'pi pi-check text-green-500'
                    : 'pi pi-times text-surface-400'
                "
                class="mr-2"
              ></i>
              <span
                :class="
                  tier.features.hasAdvancedAnalytics
                    ? 'text-surface-700 dark:text-surface-300'
                    : 'text-surface-400'
                "
              >
                Advanced analytics
              </span>
            </div>
          </div>

          <!-- Selection Indicator -->
          <div
            v-if="selectedTier === tier.id"
            class="absolute top-4 right-4 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
          >
            <i class="pi pi-check text-white text-xs"></i>
          </div>
        </div>
      </div>

      <!-- Project Details Form -->
      <Card class="max-w-2xl mx-auto shadow-xl border border-surface-200 dark:border-surface-700">
        <template #header>
          <div class="p-6 border-b border-surface-200 dark:border-surface-700">
            <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
              Project Details
            </h2>
            <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
              Tell us about your special day
            </p>
          </div>
        </template>

        <template #content>
          <form @submit.prevent="createProject" class="space-y-6">
            <!-- Project Name -->
            <div>
              <label
                for="projectName"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                Project Name *
              </label>
              <InputText
                id="projectName"
                v-model="form.projectName"
                placeholder="e.g., Sarah & John's Wedding"
                class="w-full"
                :invalid="!!errors.projectName"
                required
              />
              <small v-if="errors.projectName" class="text-red-500 mt-1 block">{{
                errors.projectName
              }}</small>
            </div>

            <!-- Wedding Date -->
            <div>
              <label
                for="weddingDate"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                Wedding Date *
              </label>
              <DatePicker
                id="weddingDate"
                v-model="form.weddingDate"
                :min-date="new Date()"
                date-format="mm/dd/yy"
                placeholder="Select your wedding date"
                class="w-full"
                :invalid="!!errors.weddingDate"
                required
              />
              <small v-if="errors.weddingDate" class="text-red-500 mt-1 block">{{
                errors.weddingDate
              }}</small>
            </div>

            <!-- Venue (Optional) -->
            <div>
              <label
                for="venue"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                Venue (Optional)
              </label>
              <InputText
                id="venue"
                v-model="form.venue"
                placeholder="e.g., Grand Ballroom, City Hall"
                class="w-full"
              />
            </div>

            <!-- Expected Guest Count -->
            <div>
              <label
                for="expectedGuests"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                Expected Guest Count *
              </label>
              <InputNumber
                id="expectedGuests"
                v-model="form.expectedGuests"
                :min="1"
                :max="
                  selectedTierData?.features.maxGuests === 'unlimited'
                    ? 10000
                    : selectedTierData?.features.maxGuests
                "
                placeholder="How many guests do you expect?"
                class="w-full"
                :invalid="!!errors.expectedGuests"
                required
              />
              <small v-if="errors.expectedGuests" class="text-red-500 mt-1 block">{{
                errors.expectedGuests
              }}</small>
              <small v-else-if="selectedTierData" class="text-surface-500 mt-1 block">
                Your {{ selectedTierData.name }} plan supports up to
                {{ ProjectTierManager.formatGuestLimit(selectedTierData.features.maxGuests) }}
                guests
              </small>
            </div>

            <!-- Error Message -->
            <Message v-if="errorMessage" severity="error" :closable="false">
              {{ errorMessage }}
            </Message>

            <!-- Submit Button -->
            <div class="flex justify-between items-center pt-4">
              <div class="text-sm text-surface-600 dark:text-surface-400">
                <strong
                  >Total:
                  {{
                    selectedTierData ? ProjectTierManager.formatPrice(selectedTierData) : 'Free'
                  }}</strong
                >
                <span v-if="selectedTierData && selectedTierData.price > 0" class="block">
                  One-time payment • Can upgrade later
                </span>
              </div>

              <Button type="submit" :loading="isLoading" size="large" :disabled="isLoading">
                <i class="pi pi-plus mr-2"></i>
                {{ selectedTierData?.price === 0 ? 'Create Free Project' : 'Continue to Payment' }}
              </Button>
            </div>
          </form>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { PROJECT_TIERS, ProjectTierManager, DEFAULT_TIER } from '@/config/subscriptionTiers'
import type { ProjectTier } from '@/config/subscriptionTiers'
import { ProjectService } from '@/services/projectService'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import DatePicker from 'primevue/datepicker'
import Button from 'primevue/button'
import Message from 'primevue/message'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const selectedTier = ref<string>(DEFAULT_TIER)
const isLoading = ref(false)
const errorMessage = ref('')

// Form data
const form = ref({
  projectName: '',
  weddingDate: null as Date | null,
  venue: '',
  expectedGuests: null as number | null,
})

// Form validation errors
const errors = ref({
  projectName: '',
  weddingDate: '',
  expectedGuests: '',
})

// Computed properties
const tiers = computed(() => ProjectTierManager.getAllTiers())

const selectedTierData = computed(() => {
  return ProjectTierManager.getTier(selectedTier.value)
})

// Methods
const validateForm = (): boolean => {
  errors.value = {
    projectName: '',
    weddingDate: '',
    expectedGuests: '',
  }

  let isValid = true

  if (!form.value.projectName.trim()) {
    errors.value.projectName = 'Project name is required'
    isValid = false
  }

  if (!form.value.weddingDate) {
    errors.value.weddingDate = 'Wedding date is required'
    isValid = false
  }

  if (!form.value.expectedGuests || form.value.expectedGuests < 1) {
    errors.value.expectedGuests = 'Expected guest count is required'
    isValid = false
  }

  // Check if guest count exceeds tier limit
  const tierData = selectedTierData.value
  if (tierData && tierData.features.maxGuests !== 'unlimited') {
    if (form.value.expectedGuests && form.value.expectedGuests > tierData.features.maxGuests) {
      errors.value.expectedGuests = `Guest count exceeds ${tierData.name} plan limit of ${tierData.features.maxGuests}`
      isValid = false
    }
  }

  return isValid
}

const createProject = async () => {
  if (!validateForm()) return

  isLoading.value = true
  errorMessage.value = ''

  try {
    // For free tier, create project immediately
    if (selectedTierData.value?.price === 0) {
      await createFreeProject()
    } else {
      // For paid tiers, redirect to payment
      await initiatePayment()
    }
  } catch (error) {
    console.error('Error creating project:', error)
    errorMessage.value = 'Failed to create project. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const createFreeProject = async () => {
  if (!authStore.firebaseUser) {
    throw new Error('User not authenticated')
  }

  if (!form.value.projectName || !form.value.weddingDate) {
    throw new Error('Please fill in all required fields')
  }

  try {
    const project = await ProjectService.createProject(authStore.firebaseUser.uid, {
      name: form.value.projectName,
      weddingDate: form.value.weddingDate,
      venue: form.value.venue,
      description: form.value.description,
      expectedGuests: form.value.expectedGuests,
      tier: selectedTier.value,
    })

    console.log('Project created successfully:', project)

    // Redirect to dashboard or project page
    router.push('/dashboard')
  } catch (error) {
    console.error('Error creating project:', error)
    throw error
  }
}

const initiatePayment = async () => {
  // TODO: Implement Stripe payment flow
  console.log('Initiating payment for:', {
    ...form.value,
    tier: selectedTier.value,
    amount: selectedTierData.value?.price,
  })

  // For now, just show a message
  errorMessage.value = 'Payment integration coming soon!'
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
  }
})
</script>
