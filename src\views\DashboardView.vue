<template>
  <AppLayout>
    <!-- Dashboard Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('dashboard.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            {{ t('dashboard.welcome', { name: authStore.userDisplayName }) }}
          </p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
          <Button :label="t('dashboard.createProject')" icon="pi pi-plus" @click="createProject" />
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="projectsStore.isLoading" class="flex justify-center items-center py-12">
      <i class="pi pi-spinner pi-spin text-4xl text-primary"></i>
    </div>

    <!-- Error State -->
    <div v-else-if="projectsStore.error" class="mb-6">
      <Message severity="error" :closable="false">
        {{ projectsStore.error }}
      </Message>
    </div>

    <!-- No Projects State -->
    <div v-else-if="!projectsStore.hasProjects" class="text-center py-12">
      <div class="mb-6">
        <i class="pi pi-folder-open text-6xl text-surface-400 mb-4"></i>
        <h2 class="text-2xl font-semibold text-surface-700 dark:text-surface-300 mb-2">
          {{ t('dashboard.noProjects.title') }}
        </h2>
        <p class="text-surface-600 dark:text-surface-400 mb-6 max-w-md mx-auto">
          {{ t('dashboard.noProjects.description') }}
        </p>
        <Button
          :label="t('dashboard.createFirstProject')"
          icon="pi pi-plus"
          size="large"
          @click="createProject"
        />
      </div>
    </div>

    <!-- Projects Dashboard -->
    <div v-else>
      <!-- Overview Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ projectsStore.totalProjects }}</div>
                <div class="text-blue-100 text-sm">{{ t('dashboard.totalProjects') }}</div>
              </div>
              <i class="pi pi-folder text-3xl text-blue-200"></i>
            </div>
          </template>
        </Card>

        <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ projectsStore.totalEvents }}</div>
                <div class="text-green-100 text-sm">{{ t('dashboard.totalEvents') }}</div>
              </div>
              <i class="pi pi-calendar text-3xl text-green-200"></i>
            </div>
          </template>
        </Card>

        <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ projectsStore.totalGuests }}</div>
                <div class="text-purple-100 text-sm">{{ t('dashboard.totalGuests') }}</div>
              </div>
              <i class="pi pi-users text-3xl text-purple-200"></i>
            </div>
          </template>
        </Card>
      </div>

      <!-- Projects Grid -->
      <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0">
            {{ t('dashboard.yourProjects') }}
          </h2>
          <Button
            :label="t('dashboard.createProject')"
            icon="pi pi-plus"
            outlined
            @click="createProject"
          />
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card
            v-for="project in projectsStore.projects"
            :key="project.id"
            class="cursor-pointer hover:shadow-lg transition-shadow duration-200"
            @click="openProject(project.id)"
          >
            <template #header>
              <div class="p-4 border-b border-surface-200 dark:border-surface-700">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <div
                      class="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center"
                    >
                      <i class="pi pi-calendar text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                      <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                        {{ project.name }}
                      </h3>
                      <p class="text-sm text-surface-600 dark:text-surface-400">
                        {{ formatDate(project.weddingDate) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span
                      :class="getTierBadgeClass(project.tier)"
                      class="px-2 py-1 text-xs font-medium rounded-full"
                    >
                      {{ project.tier.toUpperCase() }}
                    </span>
                  </div>
                </div>
              </div>
            </template>
            <template #content>
              <div class="space-y-4">
                <!-- Project Stats -->
                <div class="grid grid-cols-3 gap-4">
                  <div class="text-center">
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ project.usage.currentEvents }}
                    </div>
                    <div class="text-xs text-surface-600 dark:text-surface-400">
                      {{ t('dashboard.events') }}
                    </div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ project.usage.totalGuests }}
                    </div>
                    <div class="text-xs text-surface-600 dark:text-surface-400">
                      {{ t('dashboard.guests') }}
                    </div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ project.usage.collaboratorsAdded }}
                    </div>
                    <div class="text-xs text-surface-600 dark:text-surface-400">
                      {{ t('dashboard.collaborators') }}
                    </div>
                  </div>
                </div>

                <!-- Venue -->
                <div
                  v-if="project.venue"
                  class="flex items-center space-x-2 rtl:space-x-reverse text-sm text-surface-600 dark:text-surface-400"
                >
                  <i class="pi pi-map-marker"></i>
                  <span>{{ project.venue }}</span>
                </div>

                <!-- Quick Actions -->
                <div class="flex space-x-2 rtl:space-x-reverse pt-2">
                  <Button
                    :label="t('dashboard.openProject')"
                    size="small"
                    class="flex-1"
                    @click.stop="openProject(project.id)"
                  />
                  <Button
                    icon="pi pi-ellipsis-v"
                    size="small"
                    outlined
                    @click.stop="showProjectMenu($event, project)"
                  />
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>

    <!-- Project Context Menu -->
    <Menu ref="projectMenu" :model="projectMenuItems" :popup="true" />
  </AppLayout>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useProjectsStore } from '@/stores/projects'
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Message from 'primevue/message'
import Menu from 'primevue/menu'
import type { WeddingProject } from '@/types/user'
import type { Timestamp } from 'firebase/firestore'

const router = useRouter()
const authStore = useAuthStore()
const projectsStore = useProjectsStore()
const { t } = useLocale()

// Refs
const projectMenu = ref()
const selectedProject = ref<WeddingProject | null>(null)

// Computed
const projectMenuItems = computed(() => [
  {
    label: t('dashboard.openProject'),
    icon: 'pi pi-external-link',
    command: () => openProject(selectedProject.value?.id || ''),
  },
  {
    label: t('dashboard.editProject'),
    icon: 'pi pi-pencil',
    command: () => editProject(selectedProject.value?.id || ''),
  },
  { separator: true },
  {
    label: t('dashboard.deleteProject'),
    icon: 'pi pi-trash',
    command: () => deleteProject(selectedProject.value?.id || ''),
  },
])

// Methods
const formatDate = (timestamp: Timestamp) => {
  const date = new Date(timestamp.seconds * 1000)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date)
}

const getTierBadgeClass = (tier: string) => {
  const classes = {
    free: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    basic: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200',
    premium: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200',
    gold: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200',
  }
  return classes[tier as keyof typeof classes] || classes.free
}

const createProject = () => {
  router.push('/projects/create')
}

const openProject = (projectId: string) => {
  projectsStore.setCurrentProject(projectId)
  router.push(`/projects/${projectId}/dashboard`)
}

const editProject = (projectId: string) => {
  router.push(`/projects/${projectId}/edit`)
}

const showProjectMenu = (event: Event, project: WeddingProject) => {
  selectedProject.value = project
  projectMenu.value.toggle(event)
}

const deleteProject = async (projectId: string) => {
  if (confirm(t('dashboard.confirmDeleteProject'))) {
    try {
      await projectsStore.deleteProject(projectId)
    } catch (error) {
      console.error('Error deleting project:', error)
    }
  }
}

// Lifecycle
onMounted(async () => {
  try {
    await projectsStore.loadUserProjects()
    projectsStore.restoreCurrentProject()
  } catch (error) {
    console.error('Error loading projects:', error)
  }
})
</script>
