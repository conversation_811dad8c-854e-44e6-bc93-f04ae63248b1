import { ref, watch, onMounted } from 'vue'

const isDarkMode = ref(false)

export function useDarkMode() {
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    document.documentElement.classList.toggle('dark', isDarkMode.value)

    // Save preference to localStorage
    localStorage.setItem('darkMode', isDarkMode.value.toString())
  }

  const setDarkMode = (value: boolean) => {
    isDarkMode.value = value
    document.documentElement.classList.toggle('dark', value)
    localStorage.setItem('darkMode', value.toString())
  }

  const initDarkMode = () => {
    // Check localStorage for saved preference
    const savedDarkMode = localStorage.getItem('darkMode')

    if (savedDarkMode !== null) {
      // Use saved preference
      isDarkMode.value = savedDarkMode === 'true'
    } else {
      // Default to light mode (don't check system preference)
      isDarkMode.value = false
    }

    // Apply the dark mode class
    document.documentElement.classList.toggle('dark', isDarkMode.value)
  }

  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    // Only update if user hasn't set a preference
    if (localStorage.getItem('darkMode') === null) {
      setDarkMode(e.matches)
    }
  }

  onMounted(() => {
    initDarkMode()
    mediaQuery.addEventListener('change', handleSystemThemeChange)
  })

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initDarkMode,
  }
}
