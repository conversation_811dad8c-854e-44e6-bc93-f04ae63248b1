import { ref, watch, onMounted } from 'vue'

const isDarkMode = ref(false)

// Initialize dark mode immediately when the module loads
const initializeDarkMode = () => {
  const savedDarkMode = localStorage.getItem('darkMode')

  if (savedDarkMode !== null) {
    isDarkMode.value = savedDarkMode === 'true'
  } else {
    isDarkMode.value = false // Default to light mode
  }

  // Apply the dark mode class immediately
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

// Initialize immediately if we're in the browser
if (typeof window !== 'undefined') {
  initializeDarkMode()
}

export function useDarkMode() {
  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    document.documentElement.classList.toggle('dark', isDarkMode.value)

    // Save preference to localStorage
    localStorage.setItem('darkMode', isDarkMode.value.toString())
  }

  const setDarkMode = (value: boolean) => {
    isDarkMode.value = value
    document.documentElement.classList.toggle('dark', value)
    localStorage.setItem('darkMode', value.toString())
  }

  const initDarkMode = () => {
    // Re-initialize if needed (already done at module load)
    initializeDarkMode()
  }

  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    // Only update if user hasn't set a preference
    if (localStorage.getItem('darkMode') === null) {
      setDarkMode(e.matches)
    }
  }

  onMounted(() => {
    initDarkMode()
    mediaQuery.addEventListener('change', handleSystemThemeChange)
  })

  return {
    isDarkMode,
    toggleDarkMode,
    setDarkMode,
    initDarkMode,
  }
}
