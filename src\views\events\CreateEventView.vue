<template>
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
          Create New Event
        </h1>
        <p class="text-lg text-surface-600 dark:text-surface-400 max-w-2xl mx-auto">
          Add a new event to your wedding project. You can create multiple events like ceremony, reception, rehearsal dinner, and more.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !project" class="flex justify-center items-center py-12">
        <ProgressSpinner />
      </div>

      <!-- Error State -->
      <Message v-else-if="errorMessage && !project" severity="error" :closable="false" class="mb-6">
        {{ errorMessage }}
      </Message>

      <!-- Main Content -->
      <div v-else-if="project" class="space-y-8">
        <!-- Project Info -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
                  {{ project.name }}
                </h3>
                <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
                  {{ formatDate(project.weddingDate) }}
                  <span v-if="project.venue" class="ml-2">• {{ project.venue }}</span>
                </p>
              </div>
              <div class="text-right text-sm text-surface-600 dark:text-surface-400">
                <div>{{ currentTierData?.name }} Plan</div>
                <div>{{ project.usage.currentEvents }}/{{ currentTierData?.features.maxEvents }} events</div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Event Creation Form -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Event Details
              </h2>
              <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                Provide information about your event
              </p>
            </div>
          </template>

          <template #content>
            <form @submit.prevent="createEvent" class="space-y-6">
              <!-- Event Name -->
              <div>
                <label
                  for="eventName"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Name *
                </label>
                <InputText
                  id="eventName"
                  v-model="form.eventName"
                  placeholder="e.g., Wedding Ceremony, Reception, Rehearsal Dinner"
                  class="w-full"
                  :invalid="!!errors.eventName"
                  required
                />
                <small v-if="errors.eventName" class="text-red-500 mt-1 block">{{
                  errors.eventName
                }}</small>
              </div>

              <!-- Event Type -->
              <div>
                <label
                  for="eventType"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Type *
                </label>
                <Dropdown
                  id="eventType"
                  v-model="form.eventType"
                  :options="eventTypes"
                  option-label="label"
                  option-value="value"
                  placeholder="Select event type"
                  class="w-full"
                  :invalid="!!errors.eventType"
                  required
                />
                <small v-if="errors.eventType" class="text-red-500 mt-1 block">{{
                  errors.eventType
                }}</small>
              </div>

              <!-- Event Date -->
              <div>
                <label
                  for="eventDate"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Date *
                </label>
                <DatePicker
                  id="eventDate"
                  v-model="form.eventDate"
                  :min-date="new Date()"
                  date-format="mm/dd/yy"
                  placeholder="Select event date"
                  class="w-full"
                  :invalid="!!errors.eventDate"
                  required
                />
                <small v-if="errors.eventDate" class="text-red-500 mt-1 block">{{
                  errors.eventDate
                }}</small>
              </div>

              <!-- Event Time -->
              <div>
                <label
                  for="eventTime"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Event Time (Optional)
                </label>
                <InputText
                  id="eventTime"
                  v-model="form.eventTime"
                  placeholder="e.g., 3:00 PM, 6:30 PM"
                  class="w-full"
                />
              </div>

              <!-- Venue -->
              <div>
                <label
                  for="venue"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Venue (Optional)
                </label>
                <InputText
                  id="venue"
                  v-model="form.venue"
                  placeholder="e.g., St. Mary's Church, Grand Ballroom"
                  class="w-full"
                />
              </div>

              <!-- Description -->
              <div>
                <label
                  for="description"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Description (Optional)
                </label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  placeholder="Add any additional details about this event..."
                  class="w-full"
                  rows="3"
                />
              </div>

              <!-- Error Message -->
              <Message v-if="createErrorMessage" severity="error" :closable="false">
                {{ createErrorMessage }}
              </Message>

              <!-- Tier Limit Warning -->
              <Message 
                v-if="isAtEventLimit" 
                severity="warn" 
                :closable="false"
              >
                You've reached the maximum number of events ({{ currentTierData?.features.maxEvents }}) for your {{ currentTierData?.name }} plan. 
                <router-link :to="`/projects/${project.id}/upgrade`" class="underline">
                  Upgrade your plan
                </router-link> to create more events.
              </Message>

              <!-- Submit Button -->
              <div class="flex justify-between items-center pt-4">
                <Button
                  type="button"
                  severity="secondary"
                  @click="$router.push(`/projects/${project.id}/events`)"
                >
                  <i class="pi pi-arrow-left mr-2"></i>
                  Back to Events
                </Button>

                <Button 
                  type="submit" 
                  :loading="isCreating" 
                  :disabled="isCreating || isAtEventLimit"
                >
                  <i class="pi pi-plus mr-2"></i>
                  Create Event
                </Button>
              </div>
            </form>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ProjectTierManager } from '@/config/subscriptionTiers'
import { ProjectService } from '@/services/projectService'
import type { WeddingProject } from '@/types/user'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import Dropdown from 'primevue/dropdown'
import DatePicker from 'primevue/datepicker'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const project = ref<WeddingProject | null>(null)
const isLoading = ref(true)
const isCreating = ref(false)
const errorMessage = ref('')
const createErrorMessage = ref('')

// Form data
const form = ref({
  eventName: '',
  eventType: '',
  eventDate: null as Date | null,
  eventTime: '',
  venue: '',
  description: '',
})

// Form validation errors
const errors = ref({
  eventName: '',
  eventType: '',
  eventDate: '',
})

// Event type options
const eventTypes = [
  { label: 'Wedding Ceremony', value: 'ceremony' },
  { label: 'Reception', value: 'reception' },
  { label: 'Rehearsal Dinner', value: 'rehearsal' },
  { label: 'Engagement Party', value: 'party' },
  { label: 'Other', value: 'other' },
]

// Computed properties
const currentTierData = computed(() => {
  if (!project.value) return null
  return ProjectTierManager.getTier(project.value.tier)
})

const isAtEventLimit = computed(() => {
  if (!project.value || !currentTierData.value) return false
  return project.value.usage.currentEvents >= currentTierData.value.features.maxEvents
})

// Methods
const formatDate = (timestamp: any): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp.seconds * 1000)
  return date.toLocaleDateString()
}

const loadProject = async () => {
  const projectId = route.params.projectId as string
  if (!projectId) {
    errorMessage.value = 'Project ID is required'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    const projectData = await ProjectService.getProject(projectId)
    if (!projectData) {
      errorMessage.value = 'Project not found'
      return
    }

    project.value = projectData
  } catch (error) {
    console.error('Error loading project:', error)
    errorMessage.value = 'Failed to load project'
  } finally {
    isLoading.value = false
  }
}

const validateForm = (): boolean => {
  errors.value = {
    eventName: '',
    eventType: '',
    eventDate: '',
  }

  let isValid = true

  if (!form.value.eventName.trim()) {
    errors.value.eventName = 'Event name is required'
    isValid = false
  }

  if (!form.value.eventType) {
    errors.value.eventType = 'Event type is required'
    isValid = false
  }

  if (!form.value.eventDate) {
    errors.value.eventDate = 'Event date is required'
    isValid = false
  }

  return isValid
}

const createEvent = async () => {
  if (!validateForm() || !project.value) return

  // Check event limit
  if (isAtEventLimit.value) {
    createErrorMessage.value = 'You have reached the maximum number of events for your current plan.'
    return
  }

  isCreating.value = true
  createErrorMessage.value = ''

  try {
    // TODO: Implement actual event creation service
    console.log('Creating event:', {
      projectId: project.value.id,
      ...form.value,
    })

    // For now, just simulate success and redirect
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Redirect to events list
    router.push(`/projects/${project.value.id}/events`)
  } catch (error) {
    console.error('Error creating event:', error)
    createErrorMessage.value = 'Failed to create event. Please try again.'
  } finally {
    isCreating.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  loadProject()
})
</script>
