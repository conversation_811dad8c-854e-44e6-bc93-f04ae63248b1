<template>
  <AppLayout>
    <!-- Loading State -->
    <div v-if="projectsStore.isLoading" class="flex justify-center items-center py-12">
      <i class="pi pi-spinner pi-spin text-4xl text-primary"></i>
    </div>

    <!-- Error State -->
    <div v-else-if="projectsStore.error" class="mb-6">
      <Message severity="error" :closable="false">
        {{ projectsStore.error }}
      </Message>
    </div>

    <!-- No Current Project -->
    <div v-else-if="!currentProject" class="text-center py-12">
      <div class="mb-6">
        <i class="pi pi-folder-open text-6xl text-surface-400 mb-4"></i>
        <h2 class="text-2xl font-semibold text-surface-700 dark:text-surface-300 mb-2">
          {{ t('project.noProjectSelected') }}
        </h2>
        <p class="text-surface-600 dark:text-surface-400 mb-6 max-w-md mx-auto">
          {{ t('project.selectProjectDescription') }}
        </p>
        <Button
          :label="t('dashboard.yourProjects')"
          icon="pi pi-arrow-left"
          @click="router.push('/')"
        />
      </div>
    </div>

    <!-- Project Dashboard -->
    <div v-else>
      <!-- Project Header -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-4 rtl:space-x-reverse">
            <Button
              icon="pi pi-arrow-left"
              text
              @click="router.push('/')"
              class="text-surface-600 dark:text-surface-400"
            />
            <div>
              <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
                {{ currentProject.name }}
              </h1>
              <div class="flex items-center space-x-4 rtl:space-x-reverse mt-1">
                <p class="text-surface-600 dark:text-surface-400">
                  {{ formatDate(currentProject.weddingDate) }}
                </p>
                <span
                  :class="getTierBadgeClass(currentProject.tier)"
                  class="px-2 py-1 text-xs font-medium rounded-full"
                >
                  {{ currentProject.tier.toUpperCase() }}
                </span>
              </div>
            </div>
          </div>
          <div class="flex space-x-3 rtl:space-x-reverse">
            <Button :label="t('events.createEvent')" icon="pi pi-plus" @click="createEvent" />
            <Button
              :label="t('guests.addGuest')"
              icon="pi pi-user-plus"
              outlined
              @click="addGuest"
            />
          </div>
        </div>

        <!-- Venue Info -->
        <div
          v-if="currentProject.venue"
          class="flex items-center space-x-2 rtl:space-x-reverse text-surface-600 dark:text-surface-400"
        >
          <i class="pi pi-map-marker"></i>
          <span>{{ currentProject.venue }}</span>
        </div>
      </div>

      <!-- Project Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ currentProject.usage.currentEvents }}</div>
                <div class="text-blue-100 text-sm">{{ t('dashboard.events') }}</div>
              </div>
              <i class="pi pi-calendar text-3xl text-blue-200"></i>
            </div>
          </template>
        </Card>

        <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ currentProject.usage.totalGuests }}</div>
                <div class="text-green-100 text-sm">{{ t('dashboard.guests') }}</div>
              </div>
              <i class="pi pi-users text-3xl text-green-200"></i>
            </div>
          </template>
        </Card>

        <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ currentProject.usage.collaboratorsAdded }}</div>
                <div class="text-purple-100 text-sm">{{ t('dashboard.collaborators') }}</div>
              </div>
              <i class="pi pi-user-plus text-3xl text-purple-200"></i>
            </div>
          </template>
        </Card>

        <Card class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold">{{ daysUntilWedding }}</div>
                <div class="text-orange-100 text-sm">{{ t('project.daysUntilWedding') }}</div>
              </div>
              <i class="pi pi-clock text-3xl text-orange-200"></i>
            </div>
          </template>
        </Card>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Events List -->
        <div class="lg:col-span-2">
          <Card>
            <template #title>
              <div class="flex items-center justify-between">
                <span>{{ t('project.projectEvents') }}</span>
                <Button
                  :label="t('events.createEvent')"
                  icon="pi pi-plus"
                  size="small"
                  @click="createEvent"
                />
              </div>
            </template>
            <template #content>
              <div v-if="projectEvents.length === 0" class="text-center py-8">
                <i class="pi pi-calendar text-4xl text-surface-400 mb-4"></i>
                <p class="text-surface-600 dark:text-surface-400 mb-4">
                  {{ t('events.noEventsInProject') }}
                </p>
                <Button :label="t('events.createFirstEvent')" @click="createEvent" />
              </div>
              <div v-else class="space-y-4">
                <div
                  v-for="event in projectEvents"
                  :key="event.id"
                  class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors cursor-pointer"
                  @click="viewEvent(event.id)"
                >
                  <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <div
                      class="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center"
                    >
                      <i class="pi pi-calendar text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div>
                      <h4 class="font-semibold text-surface-900 dark:text-surface-0">
                        {{ event.name }}
                      </h4>
                      <p class="text-sm text-surface-600 dark:text-surface-400">
                        {{ formatEventDate(event.date) }} • {{ event.guestCount || 0 }}
                        {{ t('guests.title').toLowerCase() }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-2 rtl:space-x-reverse">
                    <Button icon="pi pi-eye" text size="small" @click.stop="viewEvent(event.id)" />
                    <Button
                      icon="pi pi-pencil"
                      text
                      size="small"
                      @click.stop="editEvent(event.id)"
                    />
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>

        <!-- Quick Actions & Project Info -->
        <div class="space-y-6">
          <!-- Quick Actions -->
          <Card>
            <template #title>{{ t('dashboard.quickActions') }}</template>
            <template #content>
              <div class="space-y-3">
                <Button
                  :label="t('events.createEvent')"
                  icon="pi pi-plus"
                  class="w-full"
                  @click="createEvent"
                />
                <Button
                  :label="t('guests.addGuest')"
                  icon="pi pi-user-plus"
                  class="w-full"
                  outlined
                  @click="addGuest"
                />
                <Button
                  :label="t('analytics.viewAnalytics')"
                  icon="pi pi-chart-bar"
                  class="w-full"
                  outlined
                  @click="viewAnalytics"
                />
                <Button
                  :label="t('project.manageCollaborators')"
                  icon="pi pi-users"
                  class="w-full"
                  outlined
                  @click="manageCollaborators"
                />
              </div>
            </template>
          </Card>

          <!-- Project Progress -->
          <Card>
            <template #title>{{ t('project.projectProgress') }}</template>
            <template #content>
              <div class="space-y-4">
                <!-- Events Progress -->
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span>{{ t('dashboard.events') }}</span>
                    <span>{{ currentProject.usage.currentEvents }} / {{ getEventLimit() }}</span>
                  </div>
                  <div class="w-full bg-surface-200 dark:bg-surface-700 rounded-full h-2">
                    <div
                      class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${getEventsProgress()}%` }"
                    ></div>
                  </div>
                </div>

                <!-- Guests Progress -->
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span>{{ t('dashboard.guests') }}</span>
                    <span>{{ currentProject.usage.totalGuests }} / {{ getGuestLimit() }}</span>
                  </div>
                  <div class="w-full bg-surface-200 dark:bg-surface-700 rounded-full h-2">
                    <div
                      class="bg-green-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${getGuestsProgress()}%` }"
                    ></div>
                  </div>
                </div>

                <!-- Collaborators Progress -->
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span>{{ t('dashboard.collaborators') }}</span>
                    <span
                      >{{ currentProject.usage.collaboratorsAdded }} /
                      {{ getCollaboratorLimit() }}</span
                    >
                  </div>
                  <div class="w-full bg-surface-200 dark:bg-surface-700 rounded-full h-2">
                    <div
                      class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${getCollaboratorsProgress()}%` }"
                    ></div>
                  </div>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useEventsStore } from '@/stores/events'
import { useLocale } from '@/composables/useLocale'
import { ProjectTierManager } from '@/config/subscriptionTiers'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Message from 'primevue/message'
import type { Timestamp } from 'firebase/firestore'

const router = useRouter()
const route = useRoute()
const projectsStore = useProjectsStore()
const eventsStore = useEventsStore()
const { t } = useLocale()

// Computed
const currentProject = computed(() => projectsStore.currentProject)

const projectEvents = computed(() => {
  // TODO: Filter events by current project
  return eventsStore.events.filter((event) => event.projectId === currentProject.value?.id)
})

const daysUntilWedding = computed(() => {
  if (!currentProject.value) return 0
  const weddingDate = new Date(currentProject.value.weddingDate.seconds * 1000)
  const today = new Date()
  const diffTime = weddingDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

// Methods
const formatDate = (timestamp: Timestamp) => {
  const date = new Date(timestamp.seconds * 1000)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}

const formatEventDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

const getTierBadgeClass = (tier: string) => {
  const classes = {
    free: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    basic: 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200',
    premium: 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200',
    gold: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200',
  }
  return classes[tier as keyof typeof classes] || classes.free
}

// Tier limits
const getEventLimit = () => {
  if (!currentProject.value) return 0
  const tier = ProjectTierManager.getTier(currentProject.value.tier)
  return tier?.features.maxEvents || 0
}

const getGuestLimit = () => {
  if (!currentProject.value) return 0
  const tier = ProjectTierManager.getTier(currentProject.value.tier)
  return tier?.features.maxGuests === 'unlimited' ? 999999 : tier?.features.maxGuests || 0
}

const getCollaboratorLimit = () => {
  if (!currentProject.value) return 0
  const tier = ProjectTierManager.getTier(currentProject.value.tier)
  return tier?.features.maxCollaborators || 0
}

// Progress calculations
const getEventsProgress = () => {
  if (!currentProject.value) return 0
  const limit = getEventLimit()
  if (limit === 0) return 0
  return Math.min(100, (currentProject.value.usage.currentEvents / limit) * 100)
}

const getGuestsProgress = () => {
  if (!currentProject.value) return 0
  const limit = getGuestLimit()
  if (limit === 0 || limit === 999999) return 0
  return Math.min(100, (currentProject.value.usage.totalGuests / limit) * 100)
}

const getCollaboratorsProgress = () => {
  if (!currentProject.value) return 0
  const limit = getCollaboratorLimit()
  if (limit === 0) return 0
  return Math.min(100, (currentProject.value.usage.collaboratorsAdded / limit) * 100)
}

// Actions
const createEvent = () => {
  router.push(`/projects/${currentProject.value?.id}/events/create`)
}

const addGuest = () => {
  router.push(`/projects/${currentProject.value?.id}/guests/add`)
}

const viewEvent = (eventId: string) => {
  router.push(`/projects/${currentProject.value?.id}/events/${eventId}`)
}

const editEvent = (eventId: string) => {
  router.push(`/projects/${currentProject.value?.id}/events/${eventId}/edit`)
}

const viewAnalytics = () => {
  router.push(`/projects/${currentProject.value?.id}/analytics`)
}

const manageCollaborators = () => {
  router.push(`/projects/${currentProject.value?.id}/collaborators`)
}

// Lifecycle
onMounted(async () => {
  const projectId = route.params.projectId as string

  if (projectId) {
    // Ensure projects are loaded
    if (!projectsStore.hasProjects) {
      await projectsStore.loadUserProjects()
    }

    // Set current project
    projectsStore.setCurrentProject(projectId)

    // Load project events
    if (currentProject.value) {
      await eventsStore.loadProjectEvents(projectId)
    }
  }
})
</script>
