<template>
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <ProgressSpinner />
      </div>

      <!-- Error State -->
      <Message v-else-if="errorMessage" severity="error" :closable="false" class="mb-6">
        {{ errorMessage }}
      </Message>

      <!-- Main Content -->
      <div v-else-if="event" class="space-y-8">
        <!-- Header -->
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
              {{ event.name }}
            </h1>
            <div class="flex items-center space-x-4 text-surface-600 dark:text-surface-400">
              <span class="flex items-center">
                <i class="pi pi-calendar mr-2"></i>
                {{ formatDate(event.date) }}
              </span>
              <span v-if="event.time" class="flex items-center">
                <i class="pi pi-clock mr-2"></i>
                {{ event.time }}
              </span>
              <span v-if="event.venue" class="flex items-center">
                <i class="pi pi-map-marker mr-2"></i>
                {{ event.venue }}
              </span>
            </div>
          </div>
          <div class="flex space-x-3">
            <Button
              severity="secondary"
              @click="$router.push(`/projects/${event.projectId}/events/${event.id}/edit`)"
            >
              <i class="pi pi-pencil mr-2"></i>
              Edit Event
            </Button>
            <Button
              severity="secondary"
              @click="$router.push(`/projects/${event.projectId}/events`)"
            >
              <i class="pi pi-arrow-left mr-2"></i>
              Back to Events
            </Button>
          </div>
        </div>

        <!-- Event Info Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Event Status -->
          <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
            <template #content>
              <div class="text-center">
                <div class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                  <Tag 
                    :value="event.status" 
                    :severity="getStatusSeverity(event.status)"
                    class="text-sm"
                  />
                </div>
                <p class="text-surface-600 dark:text-surface-400 text-sm">Event Status</p>
              </div>
            </template>
          </Card>

          <!-- Guest Count -->
          <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
            <template #content>
              <div class="text-center">
                <div class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                  {{ event.guestCount || 0 }}
                </div>
                <p class="text-surface-600 dark:text-surface-400 text-sm">Total Guests</p>
              </div>
            </template>
          </Card>

          <!-- Confirmed Guests -->
          <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
            <template #content>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600 mb-2">
                  {{ event.confirmedGuests || 0 }}
                </div>
                <p class="text-surface-600 dark:text-surface-400 text-sm">Confirmed</p>
              </div>
            </template>
          </Card>
        </div>

        <!-- Event Details -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Event Details
              </h2>
            </div>
          </template>

          <template #content>
            <div class="space-y-6">
              <!-- Basic Info -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                    Event Type
                  </label>
                  <p class="text-surface-900 dark:text-surface-0 capitalize">
                    {{ event.type }}
                  </p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                    Created
                  </label>
                  <p class="text-surface-900 dark:text-surface-0">
                    {{ formatDate(event.createdAt) }}
                  </p>
                </div>
              </div>

              <!-- Description -->
              <div v-if="event.description">
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Description
                </label>
                <p class="text-surface-900 dark:text-surface-0">
                  {{ event.description }}
                </p>
              </div>
            </div>
          </template>
        </Card>

        <!-- Quick Actions -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Quick Actions
              </h2>
            </div>
          </template>

          <template #content>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                class="w-full"
                @click="$router.push(`/projects/${event.projectId}/guests`)"
              >
                <i class="pi pi-users mr-2"></i>
                Manage Guests
              </Button>

              <Button
                class="w-full"
                severity="secondary"
                @click="$router.push(`/projects/${event.projectId}/events/${event.id}/edit`)"
              >
                <i class="pi pi-pencil mr-2"></i>
                Edit Event
              </Button>

              <Button
                class="w-full"
                severity="help"
                @click="$router.push(`/projects/${event.projectId}`)"
              >
                <i class="pi pi-home mr-2"></i>
                Project Dashboard
              </Button>
            </div>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import type { EventBasic } from '@/types/user'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'
import Tag from 'primevue/tag'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const event = ref<EventBasic | null>(null)
const isLoading = ref(true)
const errorMessage = ref('')

// Methods
const formatDate = (timestamp: any): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp.seconds * 1000)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getStatusSeverity = (status: string) => {
  switch (status) {
    case 'planning':
      return 'info'
    case 'active':
      return 'success'
    case 'completed':
      return 'success'
    case 'cancelled':
      return 'danger'
    default:
      return 'info'
  }
}

const loadEvent = async () => {
  const eventId = route.params.eventId as string
  const projectId = route.params.projectId as string
  
  if (!eventId || !projectId) {
    errorMessage.value = 'Event ID and Project ID are required'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    // TODO: Implement actual event loading service
    // For now, create a mock event
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    event.value = {
      id: eventId,
      projectId: projectId,
      name: 'Wedding Ceremony',
      type: 'ceremony',
      date: { seconds: Date.now() / 1000, nanoseconds: 0 },
      time: '3:00 PM',
      venue: 'St. Mary\'s Church',
      description: 'The main wedding ceremony where we exchange vows.',
      status: 'planning',
      guestCount: 150,
      confirmedGuests: 75,
      pendingGuests: 50,
      createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 },
      updatedAt: { seconds: Date.now() / 1000, nanoseconds: 0 },
      createdBy: authStore.user?.uid || ''
    } as EventBasic
    
  } catch (error) {
    console.error('Error loading event:', error)
    errorMessage.value = 'Failed to load event'
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  loadEvent()
})
</script>
