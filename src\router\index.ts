import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import DashboardView from '../views/DashboardView.vue'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: DashboardView,
      meta: { requiresAuth: true },
    },
    {
      path: '/legacy',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true },
    },
    // Auth routes
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/AuthView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/AuthView.vue'),
      meta: { requiresGuest: true },
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/auth/ForgotPasswordView.vue'),
      meta: { requiresGuest: true },
    },
    // Project routes
    {
      path: '/projects/create',
      name: 'create-project',
      component: () => import('../views/projects/CreateProjectView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/dashboard',
      name: 'project-dashboard',
      component: () => import('../views/projects/ProjectDashboardView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/edit',
      name: 'edit-project',
      component: () => import('../views/projects/EditProjectView.vue'),
      meta: { requiresAuth: true },
    },
    // Project-specific routes
    {
      path: '/projects/:projectId/events',
      name: 'project-events',
      component: () => import('../views/EventsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/events/create',
      name: 'create-event',
      component: () => import('../views/events/CreateEventView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/events/:eventId',
      name: 'view-event',
      component: () => import('../views/events/EventDetailsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/events/:eventId/edit',
      name: 'edit-event',
      component: () => import('../views/events/EditEventView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/guests',
      name: 'project-guests',
      component: () => import('../views/GuestsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/guests/add',
      name: 'add-guest',
      component: () => import('../views/guests/AddGuestView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/analytics',
      name: 'project-analytics',
      component: () => import('../views/AnalyticsView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/projects/:projectId/collaborators',
      name: 'project-collaborators',
      component: () => import('../views/CollaboratorsView.vue'),
      meta: { requiresAuth: true },
    },
    // Legacy routes (redirect to dashboard for now)
    {
      path: '/events',
      redirect: '/',
    },
    {
      path: '/guests',
      redirect: '/',
    },
    {
      path: '/analytics',
      redirect: '/',
    },
    {
      path: '/collaborators',
      redirect: '/',
    },
    {
      path: '/billing',
      name: 'billing',
      component: () => import('../views/BillingView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: { requiresAuth: true },
    },
  ],
})

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Auth should already be initialized, but check if ready
  if (!authStore.authIsReady) {
    // If auth isn't ready yet, allow navigation but it will be handled by the auth state change
    next()
    return
  }

  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth)
  const requiresGuest = to.matched.some((record) => record.meta.requiresGuest)
  const isAuthenticated = authStore.isAuthenticated

  if (requiresAuth && !isAuthenticated) {
    next({ name: 'login' })
  } else if (requiresGuest && isAuthenticated) {
    next({ name: 'dashboard' })
  } else {
    next()
  }
})

export default router
