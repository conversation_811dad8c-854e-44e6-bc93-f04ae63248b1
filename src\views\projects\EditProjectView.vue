<template>
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
          Edit Project
        </h1>
        <p class="text-lg text-surface-600 dark:text-surface-400 max-w-2xl mx-auto">
          Update your project details and upgrade your plan if needed.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !project" class="flex justify-center items-center py-12">
        <ProgressSpinner />
      </div>

      <!-- Error State -->
      <Message v-else-if="errorMessage && !project" severity="error" :closable="false" class="mb-6">
        {{ errorMessage }}
      </Message>

      <!-- Main Content -->
      <div v-else-if="project" class="space-y-8">
        <!-- Current Tier Info -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Current Plan
              </h2>
              <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                Your project is currently on the {{ currentTierData?.name }} plan
              </p>
            </div>
          </template>

          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
                  {{ currentTierData?.name }} Plan
                </h3>
                <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
                  {{ currentTierData?.description }}
                </p>
                <div class="mt-2 text-sm text-surface-500 dark:text-surface-400">
                  <span class="font-medium">
                    {{ ProjectTierManager.formatPrice(currentTierData) }}
                  </span>
                  <span v-if="currentTierData?.price > 0" class="ml-1">one-time</span>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm text-surface-600 dark:text-surface-400 space-y-1">
                  <div>{{ currentTierData?.features.maxEvents }} event{{ currentTierData?.features.maxEvents === 1 ? '' : 's' }}</div>
                  <div>{{ ProjectTierManager.formatGuestLimit(currentTierData?.features.maxGuests) }} guests</div>
                  <div>{{ currentTierData?.features.maxCollaborators }} collaborator{{ currentTierData?.features.maxCollaborators === 1 ? '' : 's' }}</div>
                </div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Project Details Form -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Project Details
              </h2>
              <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                Update your project information
              </p>
            </div>
          </template>

          <template #content>
            <form @submit.prevent="updateProject" class="space-y-6">
              <!-- Project Name -->
              <div>
                <label
                  for="projectName"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Project Name *
                </label>
                <InputText
                  id="projectName"
                  v-model="form.projectName"
                  placeholder="e.g., Sarah & John's Wedding"
                  class="w-full"
                  :invalid="!!errors.projectName"
                  required
                />
                <small v-if="errors.projectName" class="text-red-500 mt-1 block">{{
                  errors.projectName
                }}</small>
              </div>

              <!-- Wedding Date -->
              <div>
                <label
                  for="weddingDate"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Wedding Date *
                </label>
                <DatePicker
                  id="weddingDate"
                  v-model="form.weddingDate"
                  :min-date="new Date()"
                  date-format="mm/dd/yy"
                  placeholder="Select your wedding date"
                  class="w-full"
                  :invalid="!!errors.weddingDate"
                  required
                />
                <small v-if="errors.weddingDate" class="text-red-500 mt-1 block">{{
                  errors.weddingDate
                }}</small>
              </div>

              <!-- Venue (Optional) -->
              <div>
                <label
                  for="venue"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Venue (Optional)
                </label>
                <InputText
                  id="venue"
                  v-model="form.venue"
                  placeholder="e.g., Grand Ballroom, City Hall"
                  class="w-full"
                />
              </div>

              <!-- Expected Guest Count -->
              <div>
                <label
                  for="expectedGuests"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Expected Guest Count *
                </label>
                <InputNumber
                  id="expectedGuests"
                  v-model="form.expectedGuests"
                  :min="1"
                  :max="
                    currentTierData?.features.maxGuests === 'unlimited'
                      ? 10000
                      : currentTierData?.features.maxGuests
                  "
                  placeholder="How many guests do you expect?"
                  class="w-full"
                  :invalid="!!errors.expectedGuests"
                  required
                />
                <small v-if="errors.expectedGuests" class="text-red-500 mt-1 block">{{
                  errors.expectedGuests
                }}</small>
                <small v-else-if="currentTierData" class="text-surface-500 mt-1 block">
                  Your {{ currentTierData.name }} plan supports up to
                  {{ ProjectTierManager.formatGuestLimit(currentTierData.features.maxGuests) }}
                  guests
                </small>
              </div>

              <!-- Description (Optional) -->
              <div>
                <label
                  for="description"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Description (Optional)
                </label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  placeholder="Add any additional details about your project..."
                  class="w-full"
                  rows="3"
                />
              </div>

              <!-- Error Message -->
              <Message v-if="updateErrorMessage" severity="error" :closable="false">
                {{ updateErrorMessage }}
              </Message>

              <!-- Success Message -->
              <Message v-if="successMessage" severity="success" :closable="false">
                {{ successMessage }}
              </Message>

              <!-- Submit Button -->
              <div class="flex justify-between items-center pt-4">
                <Button
                  type="button"
                  severity="secondary"
                  @click="$router.push(`/projects/${project.id}`)"
                >
                  <i class="pi pi-arrow-left mr-2"></i>
                  Back to Project
                </Button>

                <Button type="submit" :loading="isUpdating" :disabled="isUpdating">
                  <i class="pi pi-save mr-2"></i>
                  Save Changes
                </Button>
              </div>
            </form>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ProjectTierManager } from '@/config/subscriptionTiers'
import { ProjectService } from '@/services/projectService'
import type { WeddingProject } from '@/types/user'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import DatePicker from 'primevue/datepicker'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const project = ref<WeddingProject | null>(null)
const isLoading = ref(true)
const isUpdating = ref(false)
const errorMessage = ref('')
const updateErrorMessage = ref('')
const successMessage = ref('')

// Form data
const form = ref({
  projectName: '',
  weddingDate: null as Date | null,
  venue: '',
  expectedGuests: null as number | null,
  description: '',
})

// Form validation errors
const errors = ref({
  projectName: '',
  weddingDate: '',
  expectedGuests: '',
})

// Computed properties
const currentTierData = computed(() => {
  if (!project.value) return null
  return ProjectTierManager.getTier(project.value.tier)
})

// Methods
const loadProject = async () => {
  const projectId = route.params.projectId as string
  if (!projectId) {
    errorMessage.value = 'Project ID is required'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    const projectData = await ProjectService.getProject(projectId)
    if (!projectData) {
      errorMessage.value = 'Project not found'
      return
    }

    project.value = projectData
    
    // Populate form with project data
    form.value = {
      projectName: projectData.name,
      weddingDate: new Date(projectData.weddingDate.seconds * 1000),
      venue: projectData.venue || '',
      expectedGuests: projectData.expectedGuests,
      description: projectData.description || '',
    }
  } catch (error) {
    console.error('Error loading project:', error)
    errorMessage.value = 'Failed to load project'
  } finally {
    isLoading.value = false
  }
}

const validateForm = (): boolean => {
  errors.value = {
    projectName: '',
    weddingDate: '',
    expectedGuests: '',
  }

  let isValid = true

  if (!form.value.projectName.trim()) {
    errors.value.projectName = 'Project name is required'
    isValid = false
  }

  if (!form.value.weddingDate) {
    errors.value.weddingDate = 'Wedding date is required'
    isValid = false
  }

  if (!form.value.expectedGuests || form.value.expectedGuests < 1) {
    errors.value.expectedGuests = 'Expected guest count is required'
    isValid = false
  }

  // Check if guest count exceeds tier limit
  const tierData = currentTierData.value
  if (tierData && tierData.features.maxGuests !== 'unlimited') {
    if (form.value.expectedGuests && form.value.expectedGuests > tierData.features.maxGuests) {
      errors.value.expectedGuests = `Guest count exceeds ${tierData.name} plan limit of ${tierData.features.maxGuests}`
      isValid = false
    }
  }

  return isValid
}

const updateProject = async () => {
  if (!validateForm() || !project.value) return

  isUpdating.value = true
  updateErrorMessage.value = ''
  successMessage.value = ''

  try {
    const updates: Partial<WeddingProject> = {
      name: form.value.projectName,
      weddingDate: {
        seconds: Math.floor(form.value.weddingDate!.getTime() / 1000),
        nanoseconds: 0,
      },
      venue: form.value.venue,
      expectedGuests: form.value.expectedGuests!,
      description: form.value.description,
    }

    await ProjectService.updateProject(project.value.id, updates)
    
    // Update local project data
    Object.assign(project.value, updates)
    
    successMessage.value = 'Project updated successfully!'
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
  } catch (error) {
    console.error('Error updating project:', error)
    updateErrorMessage.value = 'Failed to update project. Please try again.'
  } finally {
    isUpdating.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  loadProject()
})
</script>
