import { defineStore } from 'pinia'
import { ProjectService } from '@/services/projectService'
import type { WeddingProject } from '@/types/user'
import { useAuthStore } from './auth'

interface ProjectsState {
  projects: WeddingProject[]
  currentProject: WeddingProject | null
  isLoading: boolean
  error: string | null
}

export const useProjectsStore = defineStore('projects', {
  state: (): ProjectsState => ({
    projects: [],
    currentProject: null,
    isLoading: false,
    error: null,
  }),

  getters: {
    hasProjects: (state) => state.projects.length > 0,
    currentProjectId: (state) => state.currentProject?.id || null,
    projectById: (state) => (id: string) => state.projects.find(p => p.id === id),
    
    // Project stats
    totalProjects: (state) => state.projects.length,
    totalEvents: (state) => state.projects.reduce((sum, p) => sum + p.usage.currentEvents, 0),
    totalGuests: (state) => state.projects.reduce((sum, p) => sum + p.usage.totalGuests, 0),
    
    // Current project stats
    currentProjectStats: (state) => {
      if (!state.currentProject) return null
      return {
        events: state.currentProject.usage.currentEvents,
        guests: state.currentProject.usage.totalGuests,
        collaborators: state.currentProject.usage.collaboratorsAdded,
        tier: state.currentProject.tier,
        tierLimits: state.currentProject.tierLimits,
      }
    },
  },

  actions: {
    async loadUserProjects() {
      const authStore = useAuthStore()
      if (!authStore.firebaseUser) {
        throw new Error('User not authenticated')
      }

      try {
        this.isLoading = true
        this.error = null
        
        this.projects = await ProjectService.getUserProjects(authStore.firebaseUser.uid)
        
        // Set current project if none selected and projects exist
        if (!this.currentProject && this.projects.length > 0) {
          this.currentProject = this.projects[0]
          this.persistCurrentProject()
        }
      } catch (error) {
        console.error('Error loading user projects:', error)
        this.error = error instanceof Error ? error.message : 'Failed to load projects'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async createProject(projectData: any) {
      const authStore = useAuthStore()
      if (!authStore.firebaseUser) {
        throw new Error('User not authenticated')
      }

      try {
        this.isLoading = true
        this.error = null
        
        const newProject = await ProjectService.createProject(authStore.firebaseUser.uid, projectData)
        this.projects.push(newProject)
        
        // Set as current project
        this.setCurrentProject(newProject.id)
        
        return newProject
      } catch (error) {
        console.error('Error creating project:', error)
        this.error = error instanceof Error ? error.message : 'Failed to create project'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    setCurrentProject(projectId: string) {
      const project = this.projects.find(p => p.id === projectId)
      if (project) {
        this.currentProject = project
        this.persistCurrentProject()
      } else {
        console.warn(`Project with ID ${projectId} not found`)
      }
    },

    persistCurrentProject() {
      if (this.currentProject) {
        localStorage.setItem('currentProjectId', this.currentProject.id)
      } else {
        localStorage.removeItem('currentProjectId')
      }
    },

    restoreCurrentProject() {
      const savedProjectId = localStorage.getItem('currentProjectId')
      if (savedProjectId && this.projects.length > 0) {
        const project = this.projects.find(p => p.id === savedProjectId)
        if (project) {
          this.currentProject = project
        } else {
          // Fallback to first project if saved project not found
          this.currentProject = this.projects[0]
          this.persistCurrentProject()
        }
      }
    },

    async refreshCurrentProject() {
      if (!this.currentProject) return

      try {
        const updatedProject = await ProjectService.getProject(this.currentProject.id)
        if (updatedProject) {
          // Update in projects array
          const index = this.projects.findIndex(p => p.id === this.currentProject!.id)
          if (index !== -1) {
            this.projects[index] = updatedProject
          }
          this.currentProject = updatedProject
        }
      } catch (error) {
        console.error('Error refreshing current project:', error)
      }
    },

    async deleteProject(projectId: string) {
      try {
        this.isLoading = true
        this.error = null
        
        await ProjectService.deleteProject(projectId)
        
        // Remove from local state
        this.projects = this.projects.filter(p => p.id !== projectId)
        
        // If deleted project was current, switch to another
        if (this.currentProject?.id === projectId) {
          this.currentProject = this.projects.length > 0 ? this.projects[0] : null
          this.persistCurrentProject()
        }
      } catch (error) {
        console.error('Error deleting project:', error)
        this.error = error instanceof Error ? error.message : 'Failed to delete project'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    clearProjects() {
      this.projects = []
      this.currentProject = null
      localStorage.removeItem('currentProjectId')
    },
  },
})
