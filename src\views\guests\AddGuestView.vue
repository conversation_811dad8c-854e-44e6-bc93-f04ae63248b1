<template>
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-4">
          Add New Guest
        </h1>
        <p class="text-lg text-surface-600 dark:text-surface-400 max-w-2xl mx-auto">
          Add a new guest to your wedding project. You can set their expected attendance for each event.
        </p>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading && !project" class="flex justify-center items-center py-12">
        <ProgressSpinner />
      </div>

      <!-- Error State -->
      <Message v-else-if="errorMessage && !project" severity="error" :closable="false" class="mb-6">
        {{ errorMessage }}
      </Message>

      <!-- Main Content -->
      <div v-else-if="project" class="space-y-8">
        <!-- Project Info -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #content>
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
                  {{ project.name }}
                </h3>
                <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
                  {{ formatDate(project.weddingDate) }}
                  <span v-if="project.venue" class="ml-2">• {{ project.venue }}</span>
                </p>
              </div>
              <div class="text-right text-sm text-surface-600 dark:text-surface-400">
                <div>{{ currentTierData?.name }} Plan</div>
                <div>{{ project.usage.totalGuests }}/{{ formatGuestLimit(currentTierData?.features.maxGuests) }} guests</div>
              </div>
            </div>
          </template>
        </Card>

        <!-- Guest Form -->
        <Card class="shadow-lg border border-surface-200 dark:border-surface-700">
          <template #header>
            <div class="p-6 border-b border-surface-200 dark:border-surface-700">
              <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
                Guest Information
              </h2>
              <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                Provide details about the guest
              </p>
            </div>
          </template>

          <template #content>
            <form @submit.prevent="addGuest" class="space-y-6">
              <!-- Guest Name -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    for="firstName"
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    First Name *
                  </label>
                  <InputText
                    id="firstName"
                    v-model="form.firstName"
                    placeholder="Enter first name"
                    class="w-full"
                    :invalid="!!errors.firstName"
                    required
                  />
                  <small v-if="errors.firstName" class="text-red-500 mt-1 block">{{
                    errors.firstName
                  }}</small>
                </div>

                <div>
                  <label
                    for="lastName"
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    Last Name *
                  </label>
                  <InputText
                    id="lastName"
                    v-model="form.lastName"
                    placeholder="Enter last name"
                    class="w-full"
                    :invalid="!!errors.lastName"
                    required
                  />
                  <small v-if="errors.lastName" class="text-red-500 mt-1 block">{{
                    errors.lastName
                  }}</small>
                </div>
              </div>

              <!-- Phone Number -->
              <div>
                <label
                  for="phoneNumber"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Phone Number *
                </label>
                <InputText
                  id="phoneNumber"
                  v-model="form.phoneNumber"
                  placeholder="Enter phone number"
                  class="w-full"
                  :invalid="!!errors.phoneNumber"
                  required
                />
                <small v-if="errors.phoneNumber" class="text-red-500 mt-1 block">{{
                  errors.phoneNumber
                }}</small>
                <small class="text-surface-500 mt-1 block">
                  Phone numbers are used to prevent duplicate guests
                </small>
              </div>

              <!-- Expected Guests -->
              <div>
                <label
                  for="expectedGuests"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Expected Guests (including this person) *
                </label>
                <InputNumber
                  id="expectedGuests"
                  v-model="form.expectedGuests"
                  :min="1"
                  :max="10"
                  placeholder="How many people will this guest bring?"
                  class="w-full"
                  :invalid="!!errors.expectedGuests"
                  required
                />
                <small v-if="errors.expectedGuests" class="text-red-500 mt-1 block">{{
                  errors.expectedGuests
                }}</small>
                <small class="text-surface-500 mt-1 block">
                  Include the guest themselves in this count (e.g., guest + spouse = 2)
                </small>
              </div>

              <!-- Notes -->
              <div>
                <label
                  for="notes"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  Notes (Optional)
                </label>
                <Textarea
                  id="notes"
                  v-model="form.notes"
                  placeholder="Add any notes about this guest (dietary restrictions, seating preferences, etc.)"
                  class="w-full"
                  rows="3"
                />
              </div>

              <!-- Error Message -->
              <Message v-if="addErrorMessage" severity="error" :closable="false">
                {{ addErrorMessage }}
              </Message>

              <!-- Tier Limit Warning -->
              <Message 
                v-if="isAtGuestLimit" 
                severity="warn" 
                :closable="false"
              >
                You've reached the maximum number of guests ({{ formatGuestLimit(currentTierData?.features.maxGuests) }}) for your {{ currentTierData?.name }} plan. 
                <router-link :to="`/projects/${project.id}/upgrade`" class="underline">
                  Upgrade your plan
                </router-link> to add more guests.
              </Message>

              <!-- Submit Button -->
              <div class="flex justify-between items-center pt-4">
                <Button
                  type="button"
                  severity="secondary"
                  @click="$router.push(`/projects/${project.id}/guests`)"
                >
                  <i class="pi pi-arrow-left mr-2"></i>
                  Back to Guests
                </Button>

                <Button 
                  type="submit" 
                  :loading="isAdding" 
                  :disabled="isAdding || isAtGuestLimit"
                >
                  <i class="pi pi-plus mr-2"></i>
                  Add Guest
                </Button>
              </div>
            </form>
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ProjectTierManager } from '@/config/subscriptionTiers'
import { ProjectService } from '@/services/projectService'
import type { WeddingProject } from '@/types/user'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import InputNumber from 'primevue/inputnumber'
import Textarea from 'primevue/textarea'
import Button from 'primevue/button'
import Message from 'primevue/message'
import ProgressSpinner from 'primevue/progressspinner'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const project = ref<WeddingProject | null>(null)
const isLoading = ref(true)
const isAdding = ref(false)
const errorMessage = ref('')
const addErrorMessage = ref('')

// Form data
const form = ref({
  firstName: '',
  lastName: '',
  phoneNumber: '',
  expectedGuests: 1,
  notes: '',
})

// Form validation errors
const errors = ref({
  firstName: '',
  lastName: '',
  phoneNumber: '',
  expectedGuests: '',
})

// Computed properties
const currentTierData = computed(() => {
  if (!project.value) return null
  return ProjectTierManager.getTier(project.value.tier)
})

const isAtGuestLimit = computed(() => {
  if (!project.value || !currentTierData.value) return false
  if (currentTierData.value.features.maxGuests === 'unlimited') return false
  return project.value.usage.totalGuests >= currentTierData.value.features.maxGuests
})

// Methods
const formatDate = (timestamp: any): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp.seconds * 1000)
  return date.toLocaleDateString()
}

const formatGuestLimit = (limit: number | 'unlimited'): string => {
  return limit === 'unlimited' ? 'unlimited' : limit.toString()
}

const loadProject = async () => {
  const projectId = route.params.projectId as string
  if (!projectId) {
    errorMessage.value = 'Project ID is required'
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    
    const projectData = await ProjectService.getProject(projectId)
    if (!projectData) {
      errorMessage.value = 'Project not found'
      return
    }

    project.value = projectData
  } catch (error) {
    console.error('Error loading project:', error)
    errorMessage.value = 'Failed to load project'
  } finally {
    isLoading.value = false
  }
}

const validateForm = (): boolean => {
  errors.value = {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    expectedGuests: '',
  }

  let isValid = true

  if (!form.value.firstName.trim()) {
    errors.value.firstName = 'First name is required'
    isValid = false
  }

  if (!form.value.lastName.trim()) {
    errors.value.lastName = 'Last name is required'
    isValid = false
  }

  if (!form.value.phoneNumber.trim()) {
    errors.value.phoneNumber = 'Phone number is required'
    isValid = false
  }

  if (!form.value.expectedGuests || form.value.expectedGuests < 1) {
    errors.value.expectedGuests = 'Expected guests must be at least 1'
    isValid = false
  }

  return isValid
}

const addGuest = async () => {
  if (!validateForm() || !project.value) return

  // Check guest limit
  if (isAtGuestLimit.value) {
    addErrorMessage.value = 'You have reached the maximum number of guests for your current plan.'
    return
  }

  isAdding.value = true
  addErrorMessage.value = ''

  try {
    // TODO: Implement actual guest creation service
    console.log('Adding guest:', {
      projectId: project.value.id,
      ...form.value,
    })

    // For now, just simulate success and redirect
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Redirect to guests list
    router.push(`/projects/${project.value.id}/guests`)
  } catch (error) {
    console.error('Error adding guest:', error)
    addErrorMessage.value = 'Failed to add guest. Please try again.'
  } finally {
    isAdding.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Ensure user is authenticated
  if (!authStore.user) {
    router.push('/login')
    return
  }
  
  loadProject()
})
</script>
